# Adaptive Drift Detection Project

This project presents a comprehensive experimental framework for adaptive concept drift detection in data streams, specifically designed to address the challenges of online machine learning in non-stationary environments. Built upon the River library ecosystem, the framework systematically evaluates various drift detection algorithms across diverse scenarios involving concept drift and class imbalance dynamics. The research addresses a critical problem in streaming data analysis where the underlying data distribution changes over time, potentially degrading model performance if not properly detected and handled.

The framework implements multiple variants of the Drift Detection Method (DDM), including novel adaptive weighted approaches that enhance sensitivity to recent changes while maintaining statistical rigor. It supports comprehensive experimentation with different data generators (SEA, STAGGER, Hyperplane), configurable drift patterns, and dynamic class imbalance scenarios. The system provides automated experimental pipelines, statistical analysis tools, and visualization capabilities to facilitate rigorous evaluation of drift detection performance across multiple metrics including minority class recall, geometric mean, and prequential AUC.

## Project Structure

```
adaptive-drift-detection-project/
├── main.py                        # Main experiment runner
├── run_experiment.py              # Core experiment execution logic
├── batch_analyze.py               # Batch analysis for all scenarios
├── configs/                       # Configuration modules
│   ├── base_config.py            # Base experiment configurations
│   ├── detectors_config.py       # Drift detector configurations
│   ├── learners_config.py        # Learning algorithm configurations
│   └── scenarios_config.py       # Data scenario configurations
├── data/                          # Data generation modules
│   ├── data_generator.py         # Main data stream generator
│   ├── sea_generator.py          # SEA dataset generator
│   ├── stagger_generator.py      # STAGGER dataset generator
│   └── hyperplane_generator.py   # Hyperplane dataset generator
├── detectors/                     # Drift detection algorithms
│   ├── ddm_detector.py           # Standard DDM detector
│   ├── ddm_oci_detector.py       # DDM with OCI enhancement
│   ├── ddm_adaptive_detector.py  # Adaptive weighted DDM
│   └── ddm_dynamic_detector.py   # Dynamic imbalance-aware DDM
├── models/                        # Learning algorithms
│   └── online_bagging.py         # Online bagging ensemble
├── evaluation/                    # Evaluation and metrics
│   ├── metrics.py                # Performance metrics tracker
│   ├── evaluator.py              # Experiment evaluator
│   └── summarize_results.py      # Results summarization
├── visualization/                 # Visualization tools
│   └── plot_results.py           # Results plotting utilities
├── experiments/                   # Standalone experiments
│   └── run_ddm_baseline.py       # DDM baseline experiment
└── results/                       # Experiment results storage
```

## Module Overview

### 1. Configuration Modules (`configs/`)

#### `base_config.py`
- **Purpose**: Defines base experiment parameters and settings
- **Key Components**:
  - Random seeds for reproducibility
  - Detector warmup periods
  - Cooling periods after drift detection
  - Default experimental configurations

#### `detectors_config.py`
- **Purpose**: Configuration for all drift detection algorithms
- **Supported Detectors**:
  - `NoDetector`: Baseline without drift detection
  - `Standard_DDM`: Classic DDM algorithm
  - `DDM_OCI`: DDM with Online Class Imbalance handling
  - `Adaptive_Weighted_DDM`: Adaptive weighted DDM variants (α = 0, 0.3, 0.5, 0.8, 1.0)
  - `ImbalanceRatio_Adaptive_DDM`: Imbalance ratio adaptive DDM

#### `learners_config.py`
- **Purpose**: Configuration for learning algorithms
- **Supported Learners**:
  - `OnlineBagging`: Ensemble of Hoeffding trees with online bagging

#### `scenarios_config.py`
- **Purpose**: Defines various experimental scenarios with different drift patterns
- **Features**:
  - Concept drift schedules
  - Class imbalance patterns
  - Virtual drift scenarios
  - Multiple data generators (SEA, STAGGER, Hyperplane)

### 2. Data Generation Modules (`data/`)

#### `data_generator.py`
- **Purpose**: Main data stream generation with drift and imbalance control
- **Key Functions**:
  - `create_datastream()`: Creates configured data streams
  - `validate_config()`: Validates scenario configurations
  - `calculate_acceptance_probability()`: Handles class imbalance
- **Features**:
  - Support for multiple base generators
  - Configurable concept drift schedules
  - Dynamic class imbalance ratios
  - Virtual drift simulation

#### `sea_generator.py`
- **Purpose**: SEA (Streaming Ensemble Algorithm) dataset generator
- **Features**: Generates data with abrupt concept changes

#### `stagger_generator.py`
- **Purpose**: STAGGER dataset generator
- **Features**: Generates data with gradual concept changes

#### `hyperplane_generator.py`
- **Purpose**: Rotating hyperplane dataset generator
- **Features**: Generates data with continuous concept drift

### 3. Drift Detection Modules (`detectors/`)

#### `ddm_detector.py`
- **Purpose**: Standard DDM (Drift Detection Method) implementation
- **Algorithm**: Monitors classification error rate and its standard deviation
- **Features**:
  - Warning and drift detection thresholds
  - Statistical significance testing
  - Automatic reset after drift detection

#### `ddm_adaptive_detector.py`
- **Purpose**: Adaptive weighted DDM with enhanced sensitivity
- **Key Innovation**: Weighted error calculation with adaptive parameters
- **Parameters**:
  - `alpha`: Weighting factor for recent errors
  - `decay_factor`: Exponential decay for historical weights
  - `cooling_period`: Post-detection cooling period

#### `ddm_oci_detector.py`
- **Purpose**: DDM with Online Class Imbalance (OCI) handling
- **Features**: Adjusts detection thresholds based on class distribution

#### `ddm_dynamic_detector.py`
- **Purpose**: Dynamic DDM that adapts to imbalance ratio changes
- **Features**: Monitors both concept drift and imbalance changes

### 4. Learning Models (`models/`)

#### `online_bagging.py`
- **Purpose**: Online bagging ensemble implementation
- **Configuration**:
  - 50 Hoeffding tree base learners
  - Online bootstrap sampling
  - Majority voting for predictions

### 5. Evaluation Modules (`evaluation/`)

#### `metrics.py`
- **Purpose**: Comprehensive performance metrics tracking
- **Tracked Metrics**:
  - Minority class recall, precision, F1-score
  - Geometric mean (G-mean)
  - Prequential AUC
  - Confusion matrix components
- **Features**:
  - Real-time metric updates
  - Class-specific performance tracking
  - Reset capability for drift scenarios

#### `evaluator.py`
- **Purpose**: Experiment evaluation and result recording
- **Features**: Structured experiment execution and result storage

#### `summarize_results.py`
- **Purpose**: Aggregates and summarizes experimental results
- **Output**: Statistical summaries across multiple runs

### 6. Visualization Module (`visualization/`)

#### `plot_results.py`
- **Purpose**: Generates comprehensive visualization plots
- **Plot Types**:
  - Performance metrics comparison across detectors
  - Drift detection timeline visualization
  - Statistical significance analysis
- **Features**:
  - Automatic plot generation for all scenarios
  - Customizable visualization parameters
  - Export to PNG format

## Usage Instructions

### 1. Running Complete Experiments

#### Configure Experiment Parameters
Edit the configuration in `main.py`:
```python
USER_SCENARIOS = ['scenario5-1']  # Select scenarios to run
USER_DETECTORS = [               # Select detectors to evaluate
    'NoDetector',
    'Standard_DDM',
    'Adaptive_Weighted_DDM_0.5'
]
USER_LEARNERS = ['OnlineBagging'] # Select learning algorithms
```

#### Execute Experiments
```bash
python main.py
```

#### Analyze Results
```bash
python batch_analyze.py
```

### 2. Running Individual Experiments
```bash
python run_experiment.py
```

### 3. Running Baseline Experiments
```bash
python experiments/run_ddm_baseline.py
```

### 4. Custom Scenario Analysis
```bash
python batch_analyze.py --scenario scenario_name
```

## Experimental Framework Features

### 1. Comprehensive Drift Detection Evaluation
- Multiple drift detection algorithms with different sensitivity levels
- Systematic comparison across various drift scenarios
- Statistical significance testing

### 2. Class Imbalance Handling
- Dynamic imbalance ratio changes during stream processing
- Imbalance-aware drift detection algorithms
- Minority class performance optimization

### 3. Robust Experimental Design
- Multiple random seeds for statistical reliability
- Configurable warmup and cooling periods
- Comprehensive performance metrics

### 4. Automated Analysis Pipeline
- Batch processing of multiple scenarios
- Automatic visualization generation
- Statistical summary reports

## Key Algorithms

### DDM (Drift Detection Method)
- **Principle**: Monitors classification error rate statistics
- **Thresholds**: Warning (μ + 2σ) and Drift (μ + 3σ) levels
- **Advantages**: Parameter-free, statistically grounded

### Adaptive Weighted DDM
- **Innovation**: Weighted error calculation with recent bias
- **Formula**: `weighted_error = α × recent_error + (1-α) × historical_error`
- **Benefits**: Enhanced sensitivity to recent changes

### Imbalance-Aware DDM
- **Feature**: Adjusts detection sensitivity based on class distribution
- **Application**: Effective in scenarios with changing imbalance ratios

## Dependencies

```bash
pip install river pandas numpy matplotlib seaborn scikit-learn
```

### Required Libraries
- `river`: Online machine learning framework
- `pandas`: Data manipulation and analysis
- `numpy`: Numerical computing
- `matplotlib`: Plotting library
- `seaborn`: Statistical visualization
- `scikit-learn`: Machine learning utilities

## Output and Results

### 1. Experiment Results
- Individual run results: `results/scenario_name/detector_name/run_seed.csv`
- Aggregated summaries: `results/scenario_name/summary.csv`

### 2. Visualizations
- Performance comparison plots: `results/scenario_name/scenario_name_metrics_comparison.png`
- Drift detection timeline: `results/scenario_name/scenario_name_drift_detection.png`

### 3. Performance Metrics
- Minority class recall, precision, F1-score
- Geometric mean (G-mean)
- Prequential AUC
- Drift detection accuracy and timing

## Extensibility

### Adding New Detectors
1. Create detector class in `detectors/` directory
2. Inherit from `river.base.DriftDetector`
3. Add configuration to `configs/detectors_config.py`

### Adding New Scenarios
1. Define scenario configuration in `configs/scenarios_config.py`
2. Specify drift schedule and imbalance patterns
3. Configure data generator parameters

### Adding New Data Generators
1. Implement generator in `data/` directory
2. Follow the established interface pattern
3. Update `data_generator.py` to support new generator

## Research Applications

This framework is designed for research in:
- Concept drift detection algorithms
- Online learning under class imbalance
- Adaptive machine learning systems
- Stream mining performance evaluation

## License

This project is intended for academic and research purposes.
