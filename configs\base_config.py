#!/usr/bin/env python3

import random

RANDOM_SEEDS = [_ for _ in random.sample(range(101), 50)]

DRIFT_COOLING_PERIOD = 50

DETECTOR_WARMUP_PERIODS = {
    'Standard_DDM': 0,
    'DDM_OCI': 0,
    'Adaptive_Weighted_DDM': 0,
    'NoDetector': 0
}

DEFAULT_WARMUP_PERIOD = 0

RESULTS_DIR = "results"

PROGRESS_INTERVAL = 1000

DEFAULT_BASE_CONFIG = {
    'cooling_period': DRIFT_COOLING_PERIOD,
    'detector_warmup_periods': DETECTOR_WARMUP_PERIODS,
    'default_warmup_period': DEFAULT_WARMUP_PERIOD,
    'results_dir': RESULTS_DIR,
    'progress_interval': PROGRESS_INTERVAL
}
